package com.trs.ai.moye.data.model.request.indicator;

import com.trs.ai.moye.out.entity.OutIndicatorTimeRangeParams;
import com.trs.ai.moye.out.request.OutIndicatorTimeRangeRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指标统计周期请求
 *
 * <AUTHOR>
 * @since 2025/08/20 10:00:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndicatorStatisticPeriodRequest {

    /**
     * 开始时间参数
     */
    @Valid
    @NotNull(message = "开始时间参数不能为空")
    private OutIndicatorTimeRangeParams beginTime;

    /**
     * 结束时间参数
     */
    @Valid
    @NotNull(message = "结束时间参数不能为空")
    private OutIndicatorTimeRangeParams endTime;

    /**
     * 转换为 OutIndicatorTimeRangeRequest
     *
     * @return OutIndicatorTimeRangeRequest 对象
     */
    public OutIndicatorTimeRangeRequest toOutIndicatorTimeRangeRequest() {
        return new OutIndicatorTimeRangeRequest(this.beginTime, this.endTime);
    }
}
