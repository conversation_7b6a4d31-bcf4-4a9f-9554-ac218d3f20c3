package com.trs.ai.moye.data.model.controller;

import com.trs.ai.moye.data.model.request.DwdStartRequest;
import com.trs.ai.moye.data.model.request.IndicatorFieldRequest;
import com.trs.ai.moye.data.model.request.ModelFieldRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorAddRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorConfigUpdateRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorDetailDataSearchRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorSearchRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorSqlCreateRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorStartRequest;
import com.trs.ai.moye.data.model.request.indicator.IndicatorStatisticPeriodRequest;
import com.trs.ai.moye.data.model.response.CreateTableResponse;
import com.trs.ai.moye.data.model.response.DwdAddResponse;
import com.trs.ai.moye.data.model.response.indicator.IndicatorConfigResponse;
import com.trs.ai.moye.data.model.response.indicator.IndicatorDataPreviewResponse;
import com.trs.ai.moye.data.model.service.IndicatorModelService;
import com.trs.ai.moye.data.model.service.impl.IndicatorImportService;
import com.trs.ai.moye.llm.TranslateService;
import com.trs.ai.moye.out.service.OutIndicatorService;
import com.trs.ai.moye.out.response.OutIndicatorAvailablePeriodsResponse;
import com.trs.ai.moye.out.response.StatisticPeriodResponse;
import com.trs.ai.moye.storageengine.response.StorageSearchResponse;
import com.trs.moye.base.common.help.RepeatChecker;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.indicator.entity.DataModelIndicatorFieldResponse;
import com.trs.ai.moye.data.model.entity.IndicatorTaskExecuteRequest;
import com.trs.moye.base.data.indicator.enums.IndicatorSearchRange;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 指标库
 *
 * <AUTHOR>
 * @since 2025/5/19 17:54
 */
@RestController
@RequestMapping("/data-model/indicator")
public class IndicatorController {

    @Resource
    private IndicatorModelService indicatorModelService;

    @Resource
    private TranslateService translateService;

    @Resource
    private IndicatorImportService indicatorImportService;

    @Resource
    private OutIndicatorService outIndicatorService;


    /**
     * 生成sql
     *
     * @param request 请求参数
     * @return sql
     */
    @PostMapping("/createSql")
    public String sqlGenerate(@RequestBody IndicatorSqlCreateRequest request) {
        return translateService.sqlGenerate(request);
    }

    /**
     * 更正sql
     *
     * @param request 请求参数
     * @return sql
     */
    @PostMapping("/correctSql")
    public String sqlCorrect(@RequestBody IndicatorSqlCreateRequest request) {
        return translateService.sqlGenerate(request);
    }


    /**
     * 添加指标模型
     *
     * @param request 请求
     * @return {@link DwdAddResponse }
     * <AUTHOR>
     * @since 2025/05/19 16:43:12
     */
    @PostMapping("")
    public DwdAddResponse addIndicatorModel(@RequestBody IndicatorAddRequest request) {
        return indicatorModelService.addIndicatorModel(request);
    }

    /**
     * 获取指标配置
     *
     * @param id id
     * @return {@link String }
     * <AUTHOR>
     * @since 2025/05/20 18:16:25
     */
    @GetMapping("/{id}/config")
    public IndicatorConfigResponse getIndicatorConfig(@PathVariable Integer id) {
        return indicatorModelService.getIndicatorConfig(id);
    }

    /**
     * 获取指标字段
     *
     * @param id id
     * @return {@link String }
     * <AUTHOR>
     * @since 2025/05/20 18:16:25
     */
    @GetMapping("/{id}/fields")
    public List<DataModelIndicatorFieldResponse> getIndicatorFields(@PathVariable Integer id) {
        return indicatorModelService.getIndicatorFields(id);
    }

    /**
     * 保存指标字段列表
     *
     * @param id          id
     * @param requestList 请求列表
     * <AUTHOR>
     * @since 2025/05/29 20:23:11
     */
    @PostMapping("/{id}/field/list")
    public void saveFieldList(@PathVariable Integer id,
        @Validated @NotEmpty(message = "字段列表不能为空") @RequestBody List<IndicatorFieldRequest> requestList) {
        // 检查字段英文名重复
        RepeatChecker.checkCollection(requestList, "以下字段英文名存在重复：\n", IndicatorFieldRequest::getEnName);
        // 检查 图形化数据建模 图形化标签/边 的 字段重复 和 主键情况
        requestList.forEach(ModelFieldRequest::checkFields);
        indicatorModelService.saveFieldList(id, requestList);
    }

    /**
     * 更新指标配置
     *
     * @param id      id
     * @param request 更新请求
     * <AUTHOR>
     * @since 2025/05/20 19:19:39
     */
    @PutMapping("/{id}/config")
    public void updateIndicatorConfig(@PathVariable Integer id, @RequestBody IndicatorConfigUpdateRequest request) {
        indicatorModelService.updateIndicatorConfig(id, request);
    }

    /**
     * 启动指标库任务
     *
     * @param id      id
     * @param request 请求
     * <AUTHOR>
     * @since 2025/05/21 11:27:59
     */
    @PostMapping("/{id}/start")
    public void startIndicatorTask(@PathVariable Integer id, @RequestBody DwdStartRequest request) {
        indicatorModelService.startIndicatorTask(id, request);
    }

    /**
     * 重跑指标库任务
     *
     * @param id      id
     * @param request 请求
     */
    @PostMapping("/{id}/rerun")
    public void rerunIndicatorTask(@PathVariable Integer id, @RequestBody IndicatorStartRequest request) {
        indicatorModelService.rerunIndicatorTask(id, request);
    }

    /**
     * 停止指标库任务
     *
     * @param id id
     * <AUTHOR>
     * @since 2025/05/21 11:28:09
     */
    @PostMapping("/{id}/stop")
    public void stopIndicatorTask(@PathVariable Integer id) {
        indicatorModelService.stopIndicatorTask(id);
    }

    /**
     * 暂停指标库任务
     *
     * @param id id
     * <AUTHOR>
     * @since 2025/05/21 11:28:09
     */
    @PostMapping("/{id}/pause")
    public void pauseIndicatorTask(@PathVariable Integer id) {
        indicatorModelService.pauseIndicatorTask(id);
    }

    /**
     * 建表
     *
     * @param id 指标库id
     * @return CreateTableResponse
     */
    @PostMapping("/{id}/create-table")
    public CreateTableResponse createTable(@PathVariable Integer id) {
        return indicatorModelService.createTable(id);
    }

    /**
     * 数据预览
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/dataPreview")
    public IndicatorDataPreviewResponse dataPreview(@RequestBody IndicatorAddRequest request) {
        return indicatorModelService.dataPreview(request);
    }

    /**
     * 获取数据
     *
     * @param id          id
     * @param searchRange 搜索范围
     * @param request     参数
     * @return 结果
     * <AUTHOR>
     * @since 2025/05/27 22:15:11
     */
    @PostMapping("/{id}/query")
    public PageResponse<Map<String, Object>> queryIndicator(@PathVariable Integer id,
        @RequestParam(value = "SearchRange", required = false) IndicatorSearchRange searchRange,
        @RequestBody IndicatorDetailDataSearchRequest request) {
        return indicatorModelService.queryIndicator(id, searchRange, request);
    }

    /**
     * 数据明细
     *
     * @param dataModelId 数据建模ID
     * @param request     请求参数
     * @return {@link StorageSearchResponse}
     */
    @PostMapping("/{dataModelId}/data/page-list")
    public PageResponse<Map<String, Object>> getDataPreviewPageList(@PathVariable("dataModelId") Integer dataModelId,
        @RequestBody IndicatorSearchRequest request) {
        return indicatorModelService.getDataPreviewPageList(dataModelId, request);
    }

    /**
     * 获取可用周期
     *
     * @param id   id
     * @param year 年
     * @return {@link OutIndicatorAvailablePeriodsResponse}
     * <AUTHOR>
     * @since 2025/07/02 10:25:19
     */
    @GetMapping("/{id}/available-periods")
    public OutIndicatorAvailablePeriodsResponse getAvailablePeriods(@PathVariable Integer id, @Validated Integer year) {
        return indicatorModelService.getAvailablePeriods(id, year);
    }

    /**
     * 从要素表到指标表，依次执行批处理任务
     *
     * @param request 请求参数
     */
    @PostMapping("/execute-tasks")
    public void executeIndicatorTasks(@RequestBody IndicatorTaskExecuteRequest request) {
        indicatorImportService.updateBatchTasks(request.getParentDataModelIds(), request.getBeginTime().atStartOfDay(),
            request.getEndTime().atStartOfDay());
    }

    /**
     * 获取统计周期
     *
     * @param request 统计周期请求参数
     * @return 统计周期响应
     * <AUTHOR>
     * @since 2025/08/20 10:00:00
     */
    @PostMapping("/statistic-period")
    public StatisticPeriodResponse getStatisticPeriod(@RequestBody @Validated IndicatorStatisticPeriodRequest request) {
        return outIndicatorService.getStatisticPeriod(request.toOutIndicatorTimeRangeRequest());
    }
}
